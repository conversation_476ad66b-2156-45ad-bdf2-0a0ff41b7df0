#pragma once

// Mir200 Core Types - Following Original Delphi Project Structure
// Based on delphi/Common/Grobal2.pas and delphi/EM2Engine/M2Share.pas

// Include Windows headers first to get proper type definitions
#ifdef _WIN32
#include <windows.h>
#endif

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <array>
#include <chrono>
#include <cmath>
#include <cstdlib>

// Basic type definitions matching original Delphi types
// Use Windows types when available, otherwise define our own
#ifndef _WIN32
    using BYTE = uint8_t;
    using WORD = uint16_t;
    using DWORD = uint32_t;
#endif
using LONGWORD = uint32_t;
using ShortInt = int8_t;

// Constants from original Grobal2.pas
constexpr int MAXPATHLEN = 255;
constexpr int DIRPATHLEN = 80;
constexpr int MAP_NAME_LEN = 16;
constexpr int ACTOR_NAME_LEN = 14;
constexpr int DEFBLOCKSIZE = 16;
constexpr int BUFFERSIZE = 10000;
constexpr int DATA_BUFSIZE = 8192;
constexpr int GROUPMAX = 11;
constexpr int BAGGOLD = 5000000;
constexpr int BODYLUCKUNIT = 10;
constexpr int MAX_STATUS_ATTRIBUTE = 12;

// Direction constants
constexpr BYTE DR_UP = 0;
constexpr BYTE DR_UPRIGHT = 1;
constexpr BYTE DR_RIGHT = 2;
constexpr BYTE DR_DOWNRIGHT = 3;
constexpr BYTE DR_DOWN = 4;
constexpr BYTE DR_DOWNLEFT = 5;
constexpr BYTE DR_LEFT = 6;
constexpr BYTE DR_UPLEFT = 7;

// Equipment positions
constexpr BYTE U_DRESS = 0;
constexpr BYTE U_WEAPON = 1;
constexpr BYTE U_RIGHTHAND = 2;
constexpr BYTE U_NECKLACE = 3;
constexpr BYTE U_HELMET = 4;
constexpr BYTE U_ARMRINGL = 5;
constexpr BYTE U_ARMRINGR = 6;
constexpr BYTE U_RINGL = 7;
constexpr BYTE U_RINGR = 8;
constexpr BYTE U_BUJUK = 9;
constexpr BYTE U_BELT = 10;
constexpr BYTE U_BOOTS = 11;
constexpr BYTE U_CHARM = 12;

// Poison types
constexpr BYTE POISON_DECHEALTH = 0;
constexpr BYTE POISON_DAMAGEARMOR = 1;
constexpr BYTE POISON_LOCKSPELL = 2;
constexpr BYTE POISON_DONTMOVE = 4;
constexpr BYTE POISON_STONE = 5;

// State types
constexpr BYTE STATE_TRANSPARENT = 8;
constexpr BYTE STATE_DEFENCEUP = 9;
constexpr BYTE STATE_MAGDEFENCEUP = 10;
constexpr BYTE STATE_BUBBLEDEFENCEUP = 11;

// User modes
constexpr BYTE USERMODE_PLAYGAME = 1;
constexpr BYTE USERMODE_LOGIN = 2;
constexpr BYTE USERMODE_LOGOFF = 3;
constexpr BYTE USERMODE_NOTICE = 4;

// Race constants
constexpr BYTE RC_PLAYOBJECT = 0;
constexpr BYTE RC_PLAYMOSTER = 60;
constexpr BYTE RC_HEROOBJECT = 66;
constexpr BYTE RC_GUARD = 11;
constexpr BYTE RC_PEACENPC = 15;
constexpr BYTE RC_ANIMAL = 50;
constexpr BYTE RC_MONSTER = 80;
constexpr BYTE RC_NPC = 10;
constexpr BYTE RC_ARCHERGUARD = 112;

// Job types (matching original)
enum class JobType : BYTE {
    WARRIOR = 0,    // WARR
    WIZARD = 1,     // WIZARD
    TAOIST = 2      // TAOS
};

// Gender types
enum class GenderType : BYTE {
    MAN = 0,        // gMan
    WOMAN = 1       // gWoMan
};

// Client action types
enum class ClientAction : BYTE {
    HIT = 0,        // cHit
    MAGHIT = 1,     // cMagHit
    RUN = 2,        // cRun
    WALK = 3,       // cWalk
    DIGUP = 4,      // cDigUp
    TURN = 5        // cTurn
};

// Attack modes (from M2Share.pas)
constexpr BYTE HAM_ALL = 0;
constexpr BYTE HAM_PEACE = 1;
constexpr BYTE HAM_DEAR = 2;
constexpr BYTE HAM_MASTER = 3;
constexpr BYTE HAM_GROUP = 4;
constexpr BYTE HAM_GUILD = 5;
constexpr BYTE HAM_PKATTACK = 6;

// Basic structures matching original Delphi types
struct Point {
    int x;
    int y;
    
    Point() : x(0), y(0) {}
    Point(int x_, int y_) : x(x_), y(y_) {}
    
    bool operator==(const Point& other) const {
        return x == other.x && y == other.y;
    }
    
    bool operator!=(const Point& other) const {
        return !(*this == other);
    }
};

// TAbility structure (matching TAbility from Grobal2.pas) - Size 40 bytes
struct TAbility {
    WORD Level;             // 等级 (0x00)
    DWORD AC;               // 防御力 (0x02)
    DWORD MAC;              // 魔法防御力 (0x04)
    DWORD DC;               // 攻击力 (0x06)
    DWORD MC;               // 魔法力 (0x08)
    DWORD SC;               // 道术 (0x0A)
    WORD HP;                // 生命值 (0x0C)
    WORD MP;                // 魔法值 (0x0E)
    DWORD Exp;              // 经验值 (0x10)
    DWORD MaxExp;           // 最大经验值 (0x14)
    WORD Weight;            // 负重 (0x18)
    WORD MaxWeight;         // 最大负重 (0x1A)
    WORD WearWeight;        // 装备重量 (0x1C)
    WORD MaxWearWeight;     // 最大装备重量 (0x1E)
    WORD HandWeight;        // 手持重量 (0x20)
    WORD MaxHandWeight;     // 最大手持重量 (0x22)

    TAbility() : Level(1), AC(0), MAC(0), DC(0), MC(0), SC(0),
                 HP(0), MP(0), Exp(0), MaxExp(0), Weight(0), MaxWeight(0),
                 WearWeight(0), MaxWearWeight(0), HandWeight(0), MaxHandWeight(0) {}
};

// Compatibility alias
using Ability = TAbility;

// TNakedAbility structure (matching TNakedAbility from Grobal2.pas) - Size 20 bytes
struct TNakedAbility {
    WORD DC;                // 攻击力
    WORD MC;                // 魔法力
    WORD SC;                // 道术
    WORD AC;                // 防御力
    WORD MAC;               // 魔法防御力
    WORD HP;                // 生命值
    WORD MP;                // 魔法值
    WORD Hit;               // 准确
    WORD Speed;             // 敏捷
    WORD X2;                // 保留

    TNakedAbility() : DC(0), MC(0), SC(0), AC(0), MAC(0),
                      HP(0), MP(0), Hit(0), Speed(0), X2(0) {}
};

// Compatibility alias
using NakedAbility = TNakedAbility;

// TAddAbility structure (matching TAddAbility from Grobal2.pas) - Size 40 bytes
struct TAddAbility {
    WORD wHP;               // 生命值
    WORD wMP;               // 魔法值
    WORD wHitPoint;         // 准确
    WORD wSpeedPoint;       // 敏捷
    DWORD wAC;              // 防御力
    DWORD wMAC;             // 魔法防御力
    DWORD wDC;              // 攻击力
    DWORD wMC;              // 魔法力
    DWORD wSC;              // 道术
    BYTE btAntiPoison;      // 抗毒
    BYTE btPoisonRecover;   // 中毒恢复
    BYTE btHealthRecover;   // 体力恢复
    BYTE btSpellRecover;    // 魔法恢复
    BYTE btAntiMagic;       // 魔法躲避
    BYTE btLuck;            // 幸运
    BYTE btUnLuck;          // 诅咒
    DWORD nHitSpeed;        // 攻击速度
    BYTE btWeaponStrong;    // 武器强度

    TAddAbility() : wHP(0), wMP(0), wHitPoint(0), wSpeedPoint(0), wAC(0), wMAC(0),
                    wDC(0), wMC(0), wSC(0), btAntiPoison(0), btPoisonRecover(0),
                    btHealthRecover(0), btSpellRecover(0), btAntiMagic(0), btLuck(0),
                    btUnLuck(0), nHitSpeed(0), btWeaponStrong(0) {}
};

// Compatibility alias
using AddAbility = TAddAbility;

// Status time array (matching TStatusTime)
using StatusTime = std::array<WORD, 6>;

// Quest structures (matching original)
struct QuestUnit {
    std::array<int, 100> flags;
    
    QuestUnit() {
        flags.fill(0);
    }
};

struct QuestFlag {
    std::array<BYTE, 2> flags;
    
    QuestFlag() {
        flags.fill(0);
    }
};

// TUserItem structure (matching TUserItem from Grobal2.pas)
struct TUserItem {
    DWORD MakeIndex;        // 制造索引
    WORD wIndex;            // 物品索引
    WORD Dura;              // 持久度
    WORD DuraMax;           // 最大持久度
    BYTE btValue[14];       // 附加属性值

    TUserItem() : MakeIndex(0), wIndex(0), Dura(0), DuraMax(0) {
        std::memset(btValue, 0, sizeof(btValue));
    }
};

// Compatibility alias
using UserItem = TUserItem;

// Human use items (matching THumanUseItems)
struct HumanUseItems {
    std::array<UserItem, 13> items; // U_DRESS to U_CHARM
    
    HumanUseItems() = default;
};

// TDefaultMessage structure (matching TDefaultMessage from Grobal2.pas)
struct TDefaultMessage {
    DWORD Recog;            // 识别码
    WORD Ident;             // 消息标识
    WORD Param;             // 参数
    WORD tag;               // 标签
    WORD Series;            // 序列号

    TDefaultMessage() : Recog(0), Ident(0), Param(0), tag(0), Series(0) {}
};

// Compatibility alias
using DefaultMessage = TDefaultMessage;

// Forward declarations
class BaseObject;
class PlayObject;
class Environment;
class UserEngine;

// Smart pointer types
using BaseObjectPtr = std::shared_ptr<BaseObject>;
using PlayObjectPtr = std::shared_ptr<PlayObject>;
using EnvironmentPtr = std::shared_ptr<Environment>;
using UserEnginePtr = std::shared_ptr<UserEngine>;

// String types
using MapName = std::string;
using CharName = std::string;
using AccountName = std::string;

// Utility functions
inline DWORD GetCurrentTime() {
#ifdef _WIN32
    return static_cast<DWORD>(GetTickCount());
#else
    return static_cast<DWORD>(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
#endif
}

inline int Random(int max) {
    return rand() % max;
}

inline int Random(int min, int max) {
    return min + (rand() % (max - min + 1));
}

// Direction utilities
inline BYTE GetNextDirection(BYTE dir, bool clockwise = true) {
    if (clockwise) {
        return (dir + 1) % 8;
    } else {
        return (dir + 7) % 8;
    }
}

inline BYTE GetOppositeDirection(BYTE dir) {
    return (dir + 4) % 8;
}

// Distance calculation
inline double GetDistance(const Point& p1, const Point& p2) {
    int dx = p1.x - p2.x;
    int dy = p1.y - p2.y;
    return std::sqrt(dx * dx + dy * dy);
}

inline int GetDistance2(const Point& p1, const Point& p2) {
    int dx = abs(p1.x - p2.x);
    int dy = abs(p1.y - p2.y);
    return (dx > dy) ? dx : dy;
}
