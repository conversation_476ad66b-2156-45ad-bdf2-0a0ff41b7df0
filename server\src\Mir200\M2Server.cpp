#include "M2Server.h"
#include "Common/M2Share.h"
// Temporarily disabled for compilation:
// #include "Engine/UserEngine.h"
// #include "Engine/LocalDatabase.h"
// #include "Engine/Environment.h"
// #include "Engine/Guild.h"
// #include "Engine/Castle.h"
// #include "Network/RunSocket.h"
// #include "Network/GateSocket.h"
#include <algorithm>
#include <fstream>
#ifdef _WIN32
#include <windows.h>
#endif
#include <iostream>
#include <thread>
#include <chrono>

// M2Server implementation - Following original M2Server.dpr structure exactly

M2Server::M2Server() {
    // Initialize server state
    m_server_state.store(ServerState::STOPPED);
    m_shutdown_requested.store(false);
    m_restart_requested.store(false);
    
    // Initialize core components (temporarily disabled for compilation)
    m_user_engine = nullptr; // std::make_unique<UserEngine>();
    m_local_database = nullptr; // std::make_unique<LocalDatabase>();
    m_run_socket = nullptr; // std::make_unique<RunSocket>();
    m_gate_socket = nullptr; // std::make_unique<GateSocket>();
    
    // Initialize castle and guild management
    m_castle = nullptr;
    
    // Initialize configuration
    m_config_file = "";
    m_server_path = "";
    m_data_path = "";
    m_log_path = "";
    
    // Initialize timing and performance
    m_last_process_time = 0;
    m_process_interval = 10;        // 10ms default process interval
    m_save_interval = 300000;       // 5 minutes default save interval
    m_last_save_time = 0;
    m_start_time = GetTickCount();

    // Initialize server configuration
    m_server_name = "Mir200Server";
    m_max_user = 1000;
    m_test_server = false;
    m_service_mode = false;

    // Initialize statistics
    m_online_user_count = 0;
    m_total_user_count = 0;
    m_environment_count = 0;
    m_guild_count = 0;
    m_server_uptime = 0;
    m_memory_usage = 0;
    
    // Initialize statistics (using UserEngine's ServerStatistics structure)
    // Note: ServerStatistics is defined in UserEngine.h with different fields
    
    // Initialize error handling
    m_last_error = "";
    m_error_count = 0;
    m_last_error_time = 0;
}

M2Server::~M2Server() {
    // Ensure server is stopped
    if (IsRunning()) {
        Stop();
    }
    
    // Finalize all components
    Finalize();
}

bool M2Server::Initialize(const std::string& config_file) {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing M2Server...");
        
        // Set server state
        SetServerState(ServerState::INITIALIZING);
        
        // Store configuration file path
        m_config_file = config_file;
        
        // Initialize paths
        InitializePaths();
        
        // Create necessary directories
        if (!CreateDirectories()) {
            SetLastError("Failed to create server directories");
            return false;
        }
        
        // Load configuration
        if (!LoadConfiguration()) {
            g_functions::MainOutMessage("Warning: Failed to load configuration, using defaults");
        }
        
        // Validate configuration
        if (!ValidateConfiguration()) {
            SetLastError("Invalid server configuration");
            return false;
        }
        
        // Initialize core components
        if (!InitializeComponents()) {
            SetLastError("Failed to initialize server components");
            return false;
        }
        
        // Initialize network
        if (!InitializeNetwork()) {
            SetLastError("Failed to initialize network components");
            return false;
        }
        
        // Initialize database
        if (!InitializeDatabase()) {
            SetLastError("Failed to initialize database components");
            return false;
        }
        
        // Initialize environments
        if (!InitializeEnvironments()) {
            SetLastError("Failed to initialize game environments");
            return false;
        }
        
        // Initialize guilds
        if (!InitializeGuilds()) {
            SetLastError("Failed to initialize guild system");
            return false;
        }
        
        // Initialize castle
        if (!InitializeCastle()) {
            SetLastError("Failed to initialize castle system");
            return false;
        }
        
        // Set statistics start time (using available fields from UserEngine's ServerStatistics)
        // Note: Using different field names from UserEngine's ServerStatistics structure
        
        g_functions::MainOutMessage("M2Server initialized successfully");
        return true;
        
    TRY_END
    
    SetLastError("Exception occurred during server initialization");
    return false;
}

bool M2Server::Start() {
    TRY_BEGIN
        if (IsRunning()) {
            g_functions::MainOutMessage("Server is already running");
            return true;
        }
        
        g_functions::MainOutMessage("Starting M2Server...");
        
        // Set server state
        SetServerState(ServerState::STARTING);
        
        // Start network components
        if (m_run_socket && !m_run_socket->Start()) {
            SetLastError("Failed to start RunSocket");
            return false;
        }
        
        if (m_gate_socket && !m_gate_socket->Start()) {
            SetLastError("Failed to start GateSocket");
            return false;
        }
        
        // Start database components
        if (m_local_database && !m_local_database->Start()) {
            SetLastError("Failed to start LocalDatabase");
            return false;
        }
        
        // Start user engine
        if (m_user_engine && !m_user_engine->Start()) {
            SetLastError("Failed to start UserEngine");
            return false;
        }
        
        // Start worker threads
        StartThreads();
        
        // Set server state to running
        SetServerState(ServerState::RUNNING);
        
        g_functions::MainOutMessage("M2Server started successfully");
        return true;
        
    TRY_END
    
    SetLastError("Exception occurred during server startup");
    SetServerState(ServerState::SERVER_ERROR);
    return false;
}

void M2Server::Run() {
    TRY_BEGIN
        if (!IsRunning()) {
            g_functions::MainOutMessage("Server is not running");
            return;
        }
        
        g_functions::MainOutMessage("M2Server main loop started");
        
        // Main server loop
        while (ShouldContinueRunning()) {
            // Process server tick
            ProcessServerTick();
            
            // Sleep for process interval
            std::this_thread::sleep_for(std::chrono::milliseconds(m_process_interval));
        }
        
        g_functions::MainOutMessage("M2Server main loop ended");
        
    TRY_END
}

void M2Server::Stop() {
    TRY_BEGIN
        if (!IsRunning() && m_server_state.load() != ServerState::STARTING) {
            g_functions::MainOutMessage("Server is not running");
            return;
        }
        
        g_functions::MainOutMessage("Stopping M2Server...");
        
        // Set server state
        SetServerState(ServerState::STOPPING);
        
        // Request shutdown
        m_shutdown_requested.store(true);
        
        // Stop worker threads
        StopThreads();
        
        // Stop user engine
        if (m_user_engine) {
            m_user_engine->Stop();
        }
        
        // Stop database components
        if (m_local_database) {
            m_local_database->Stop();
        }
        
        // Stop network components
        if (m_gate_socket) {
            m_gate_socket->Stop();
        }
        
        if (m_run_socket) {
            m_run_socket->Stop();
        }
        
        // Save server data
        SaveServerData();
        
        // Set server state
        SetServerState(ServerState::STOPPED);
        
        g_functions::MainOutMessage("M2Server stopped successfully");
        
    TRY_END
}

void M2Server::Finalize() {
    TRY_BEGIN
        g_functions::MainOutMessage("Finalizing M2Server...");
        
        // Finalize all components
        FinalizeComponents();
        
        g_functions::MainOutMessage("M2Server finalized");
        
    TRY_END
}

void M2Server::EmergencyStop() {
    TRY_BEGIN
        g_functions::MainOutMessage("Emergency stop initiated!");
        
        // Force shutdown
        m_shutdown_requested.store(true);
        
        // Set error state
        SetServerState(ServerState::SERVER_ERROR);
        
        // Force stop all components
        if (m_user_engine) {
            m_user_engine->EmergencyStop();
        }
        
        if (m_local_database) {
            m_local_database->EmergencyStop();
        }
        
        if (m_gate_socket) {
            m_gate_socket->EmergencyStop();
        }
        
        if (m_run_socket) {
            m_run_socket->EmergencyStop();
        }
        
        g_functions::MainOutMessage("Emergency stop completed");
        
    TRY_END
}

// Environment management
std::shared_ptr<Environment> M2Server::GetEnvironment(const MapName& map_name) {
    std::lock_guard<std::mutex> lock(m_env_mutex);
    
    for (auto& env : m_environments) {
        if (env && env->GetMapName() == map_name) {
            return env;
        }
    }
    
    return nullptr;
}

std::shared_ptr<Environment> M2Server::CreateEnvironment(const MapName& map_name) {
    std::lock_guard<std::mutex> lock(m_env_mutex);
    
    // Check if environment already exists
    for (auto& env : m_environments) {
        if (env && env->GetMapName() == map_name) {
            return env;
        }
    }
    
    // Create new environment
    auto new_env = std::make_shared<Environment>(map_name);
    if (new_env && new_env->Initialize()) {
        m_environments.push_back(new_env);
        g_functions::MainOutMessage("Created environment for map: " + map_name);
        return new_env;
    }
    
    return nullptr;
}

void M2Server::RemoveEnvironment(const MapName& map_name) {
    std::lock_guard<std::mutex> lock(m_env_mutex);
    
    m_environments.erase(
        std::remove_if(m_environments.begin(), m_environments.end(),
            [&map_name](const std::shared_ptr<Environment>& env) {
                return env && env->GetMapName() == map_name;
            }),
        m_environments.end());
    
    g_functions::MainOutMessage("Removed environment for map: " + map_name);
}

std::vector<std::shared_ptr<Environment>> M2Server::GetAllEnvironments() {
    std::lock_guard<std::mutex> lock(m_env_mutex);
    return m_environments;
}

int M2Server::GetEnvironmentCount() const {
    std::lock_guard<std::mutex> lock(m_env_mutex);
    return static_cast<int>(m_environments.size());
}

// Guild management
std::shared_ptr<Guild> M2Server::GetGuild(const std::string& guild_name) {
    std::lock_guard<std::mutex> lock(m_guild_mutex);
    
    auto it = m_guilds.find(guild_name);
    return (it != m_guilds.end()) ? it->second : nullptr;
}

std::shared_ptr<Guild> M2Server::CreateGuild(const std::string& guild_name) {
    std::lock_guard<std::mutex> lock(m_guild_mutex);
    
    // Check if guild already exists
    auto it = m_guilds.find(guild_name);
    if (it != m_guilds.end()) {
        return it->second;
    }
    
    // Create new guild
    auto new_guild = std::make_shared<Guild>(guild_name);
    if (new_guild && new_guild->Initialize()) {
        m_guilds[guild_name] = new_guild;
        g_functions::MainOutMessage("Created guild: " + guild_name);
        return new_guild;
    }
    
    return nullptr;
}

void M2Server::RemoveGuild(const std::string& guild_name) {
    std::lock_guard<std::mutex> lock(m_guild_mutex);
    
    auto it = m_guilds.find(guild_name);
    if (it != m_guilds.end()) {
        m_guilds.erase(it);
        g_functions::MainOutMessage("Removed guild: " + guild_name);
    }
}

std::vector<std::shared_ptr<Guild>> M2Server::GetAllGuilds() {
    std::lock_guard<std::mutex> lock(m_guild_mutex);
    
    std::vector<std::shared_ptr<Guild>> result;
    for (const auto& pair : m_guilds) {
        result.push_back(pair.second);
    }
    return result;
}

int M2Server::GetGuildCount() const {
    std::lock_guard<std::mutex> lock(m_guild_mutex);
    return static_cast<int>(m_guilds.size());
}

// ============================================================================
// Private Implementation Methods - Following original M2Server.dpr logic
// ============================================================================

// Configuration management - Based on original svMain.pas FormCreate
bool M2Server::LoadConfiguration() {
    TRY_BEGIN
        g_functions::MainOutMessage("Loading M2Server configuration...");

        // Load server configuration from INI file
        if (!LoadServerConfig()) {
            g_functions::MainOutMessage("Warning: Failed to load server config, using defaults");
        }

        // Load database configuration
        if (!LoadDatabaseConfig()) {
            g_functions::MainOutMessage("Warning: Failed to load database config, using defaults");
        }

        // Load network configuration
        if (!LoadNetworkConfig()) {
            g_functions::MainOutMessage("Warning: Failed to load network config, using defaults");
        }

        // Load game configuration
        if (!LoadGameConfig()) {
            g_functions::MainOutMessage("Warning: Failed to load game config, using defaults");
        }

        g_functions::MainOutMessage("M2Server configuration loaded successfully");
        return true;

    TRY_END

    return false;
}

bool M2Server::LoadServerConfig() {
    TRY_BEGIN
        // Load basic server settings from M2Server.ini
        std::ifstream file(m_config_file);
        if (!file.is_open()) {
            return false;
        }

        std::string line;
        std::string section;

        while (std::getline(file, line)) {
            // Skip empty lines and comments
            if (line.empty() || line[0] == ';' || line[0] == '#') continue;

            // Handle sections
            if (line[0] == '[' && line.back() == ']') {
                section = line.substr(1, line.length() - 2);
                continue;
            }

            // Parse key=value pairs
            size_t pos = line.find('=');
            if (pos == std::string::npos) continue;

            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            // Parse server section settings
            if (section == "Server") {
                if (key == "ServerName") m_server_name = value;
                else if (key == "ServerPath") m_server_path = value;
                else if (key == "DataPath") m_data_path = value;
                else if (key == "LogPath") m_log_path = value;
                else if (key == "ProcessInterval") m_process_interval = std::stoi(value);
                else if (key == "SaveInterval") m_save_interval = std::stoi(value);
                else if (key == "MaxUser") m_max_user = std::stoi(value);
                else if (key == "TestServer") m_test_server = (value == "1" || value == "true");
                else if (key == "ServiceMode") m_service_mode = (value == "1" || value == "true");
            }
        }

        return true;

    TRY_END

    return false;
}

bool M2Server::LoadDatabaseConfig() {
    TRY_BEGIN
        // Database configuration will be loaded by LocalDatabase component
        // This is placeholder for future database configuration
        return true;

    TRY_END

    return false;
}

bool M2Server::LoadNetworkConfig() {
    TRY_BEGIN
        // Network configuration will be loaded by RunSocket and GateSocket components
        // This is placeholder for future network configuration
        return true;

    TRY_END

    return false;
}

bool M2Server::LoadGameConfig() {
    TRY_BEGIN
        // Game configuration will be loaded by UserEngine and other game components
        // This is placeholder for future game configuration
        return true;

    TRY_END

    return false;
}

bool M2Server::ValidateConfiguration() const {
    TRY_BEGIN
        // Validate server paths
        if (m_server_path.empty()) {
            g_functions::MainOutMessage("Error: Server path not configured");
            return false;
        }

        if (m_data_path.empty()) {
            g_functions::MainOutMessage("Error: Data path not configured");
            return false;
        }

        // Validate timing settings
        if (m_process_interval < 1 || m_process_interval > 1000) {
            g_functions::MainOutMessage("Error: Invalid process interval");
            return false;
        }

        if (m_save_interval < 1000 || m_save_interval > 3600000) {
            g_functions::MainOutMessage("Error: Invalid save interval");
            return false;
        }

        // Validate user limits
        if (m_max_user < 1 || m_max_user > 10000) {
            g_functions::MainOutMessage("Error: Invalid max user count");
            return false;
        }

        return true;

    TRY_END

    return false;
}

// Path management - Based on original project structure
void M2Server::InitializePaths() {
    TRY_BEGIN
        // Set default paths if not configured
        if (m_server_path.empty()) {
            m_server_path = ".";
        }

        if (m_data_path.empty()) {
            m_data_path = m_server_path + "/Data";
        }

        if (m_log_path.empty()) {
            m_log_path = m_server_path + "/Logs";
        }

        // Normalize paths
        std::replace(m_server_path.begin(), m_server_path.end(), '/', '\\');
        std::replace(m_data_path.begin(), m_data_path.end(), '/', '\\');
        std::replace(m_log_path.begin(), m_log_path.end(), '/', '\\');

        g_functions::MainOutMessage("Server paths initialized:");
        g_functions::MainOutMessage("  Server Path: " + m_server_path);
        g_functions::MainOutMessage("  Data Path: " + m_data_path);
        g_functions::MainOutMessage("  Log Path: " + m_log_path);

    TRY_END
}

bool M2Server::CreateDirectories() {
    TRY_BEGIN
        // Create server directories if they don't exist
        if (!g_functions::DirectoryExists(m_server_path)) {
            if (!CreateDirectoryA(m_server_path.c_str(), nullptr)) {
                g_functions::MainOutMessage("Error: Failed to create server directory: " + m_server_path);
                return false;
            }
        }

        if (!g_functions::DirectoryExists(m_data_path)) {
            if (!CreateDirectoryA(m_data_path.c_str(), nullptr)) {
                g_functions::MainOutMessage("Error: Failed to create data directory: " + m_data_path);
                return false;
            }
        }

        if (!g_functions::DirectoryExists(m_log_path)) {
            if (!CreateDirectoryA(m_log_path.c_str(), nullptr)) {
                g_functions::MainOutMessage("Error: Failed to create log directory: " + m_log_path);
                return false;
            }
        }

        // Create subdirectories
        std::vector<std::string> subdirs = {
            m_data_path + "\\Map",
            m_data_path + "\\Envir",
            m_data_path + "\\Guild",
            m_data_path + "\\Castle",
            m_data_path + "\\Backup"
        };

        for (const auto& dir : subdirs) {
            if (!g_functions::DirectoryExists(dir)) {
                CreateDirectoryA(dir.c_str(), nullptr);
            }
        }

        g_functions::MainOutMessage("Server directories created successfully");
        return true;

    TRY_END

    return false;
}

// Component initialization - Following original M2Server startup sequence
bool M2Server::InitializeComponents() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing M2Server components...");

        // Initialize core components in proper order
        // Order is important - follows original M2Server.dpr startup sequence

        // 1. Initialize UserEngine first (core player management - following original UsrEngn.pas)
        if (!m_user_engine || !m_user_engine->Initialize()) {
            SetLastError("Failed to initialize UserEngine");
            return false;
        }
        g_functions::MainOutMessage("UserEngine initialized successfully");

        // 2. Initialize LocalDatabase (data management - following original LocalDB.pas)
        // Note: LocalDatabase needs database and user_engine parameters
        if (!m_local_database || !m_local_database->Initialize(nullptr, m_user_engine.get())) {
            SetLastError("Failed to initialize LocalDatabase");
            return false;
        }
        g_functions::MainOutMessage("LocalDatabase initialized successfully");

        // 3. Set cross-references between components (following original pattern)
        if (m_user_engine && m_local_database) {
            m_user_engine->SetLocalDatabase(m_local_database.get());
        }

        g_functions::MainOutMessage("M2Server components initialized successfully");
        return true;

    TRY_END

    return false;
}

bool M2Server::InitializeNetwork() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing network components...");

        // Initialize RunSocket (main game socket - following original RunSock.pas)
        if (!m_run_socket || !m_run_socket->Initialize()) {
            SetLastError("Failed to initialize RunSocket");
            return false;
        }
        g_functions::MainOutMessage("RunSocket initialized successfully");

        // Initialize GateSocket (gate communication socket - following original GateSocket pattern)
        if (!m_gate_socket || !m_gate_socket->Initialize()) {
            SetLastError("Failed to initialize GateSocket");
            return false;
        }
        g_functions::MainOutMessage("GateSocket initialized successfully");

        g_functions::MainOutMessage("Network components initialized successfully");
        return true;

    TRY_END

    return false;
}

bool M2Server::InitializeDatabase() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing database components...");

        // Database initialization is handled by LocalDatabase component
        // Additional database setup can be added here if needed

        g_functions::MainOutMessage("Database components initialized successfully");
        return true;

    TRY_END

    return false;
}

bool M2Server::InitializeEnvironments() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing game environments...");

        // Environment initialization will be handled by Environment system
        // This is placeholder for future environment management

        g_functions::MainOutMessage("Game environments initialized successfully");
        return true;

    TRY_END

    return false;
}

bool M2Server::InitializeGuilds() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing guild system...");

        // Guild system initialization
        // Load existing guilds from data files

        g_functions::MainOutMessage("Guild system initialized successfully");
        return true;

    TRY_END

    return false;
}

bool M2Server::InitializeCastle() {
    TRY_BEGIN
        g_functions::MainOutMessage("Initializing castle system...");

        // Castle system initialization
        // Load castle data and configuration

        g_functions::MainOutMessage("Castle system initialized successfully");
        return true;

    TRY_END

    return false;
}

// Thread management - Based on original M2Server timer system
void M2Server::StartThreads() {
    TRY_BEGIN
        g_functions::MainOutMessage("Starting M2Server worker threads...");

        // Start main processing thread (equivalent to original RunTimer)
        m_main_thread = std::thread(&M2Server::MainThreadProc, this);

        // Start network processing thread
        m_network_thread = std::thread(&M2Server::NetworkThreadProc, this);

        // Start database processing thread
        m_database_thread = std::thread(&M2Server::DatabaseThreadProc, this);

        // Start timer processing thread (equivalent to original Timer1)
        m_timer_thread = std::thread(&M2Server::TimerThreadProc, this);

        g_functions::MainOutMessage("M2Server worker threads started successfully");

    TRY_END
}

void M2Server::StopThreads() {
    TRY_BEGIN
        g_functions::MainOutMessage("Stopping M2Server worker threads...");

        // Signal threads to stop
        m_shutdown_requested.store(true);

        // Wait for threads to finish
        if (m_main_thread.joinable()) {
            m_main_thread.join();
        }

        if (m_network_thread.joinable()) {
            m_network_thread.join();
        }

        if (m_database_thread.joinable()) {
            m_database_thread.join();
        }

        if (m_timer_thread.joinable()) {
            m_timer_thread.join();
        }

        g_functions::MainOutMessage("M2Server worker threads stopped successfully");

    TRY_END
}

// Main thread procedure - Equivalent to original RunTimer.OnTimer
void M2Server::MainThreadProc() {
    TRY_BEGIN
        g_functions::MainOutMessage("Main thread started");

        while (!m_shutdown_requested.load() && IsRunning()) {
            // Process main server tick
            ProcessServerTick();

            // Sleep for process interval
            std::this_thread::sleep_for(std::chrono::milliseconds(m_process_interval));
        }

        g_functions::MainOutMessage("Main thread ended");

    TRY_END
}

// Network thread procedure - Handle network processing
void M2Server::NetworkThreadProc() {
    TRY_BEGIN
        g_functions::MainOutMessage("Network thread started");

        while (!m_shutdown_requested.load() && IsRunning()) {
            // Process network messages
            ProcessNetworkMessages();

            // Sleep for network interval
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        g_functions::MainOutMessage("Network thread ended");

    TRY_END
}

// Database thread procedure - Handle database operations
void M2Server::DatabaseThreadProc() {
    TRY_BEGIN
        g_functions::MainOutMessage("Database thread started");

        while (!m_shutdown_requested.load() && IsRunning()) {
            // Process database operations
            ProcessDatabaseOperations();

            // Sleep for database interval
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        g_functions::MainOutMessage("Database thread ended");

    TRY_END
}

// Timer thread procedure - Equivalent to original Timer1.OnTimer
void M2Server::TimerThreadProc() {
    TRY_BEGIN
        g_functions::MainOutMessage("Timer thread started");

        while (!m_shutdown_requested.load() && IsRunning()) {
            // Process timer events
            ProcessTimerEvents();

            // Process maintenance tasks
            ProcessMaintenanceTasks();

            // Sleep for timer interval (2 seconds like original Timer1)
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
        }

        g_functions::MainOutMessage("Timer thread ended");

    TRY_END
}

// Server processing - Following original M2Server logic
void M2Server::ProcessServerTick() {
    TRY_BEGIN
        DWORD current_time = GetTickCount();

        // Update last process time
        m_last_process_time = current_time;

        // Process user engine
        if (m_user_engine) {
            m_user_engine->ProcessUsers();
        }

        // Process environments
        ProcessEnvironments();

        // Process guilds
        ProcessGuilds();

        // Process castle
        ProcessCastle();

        // Check for save interval
        if (current_time - m_last_save_time >= m_save_interval) {
            SaveServerData();
            m_last_save_time = current_time;
        }

    TRY_END
}

void M2Server::ProcessNetworkMessages() {
    TRY_BEGIN
        // Process RunSocket messages
        if (m_run_socket) {
            m_run_socket->ProcessMessages();
        }

        // Process GateSocket messages
        if (m_gate_socket) {
            m_gate_socket->ProcessMessages();
        }

    TRY_END
}

void M2Server::ProcessDatabaseOperations() {
    TRY_BEGIN
        // Process LocalDatabase operations
        if (m_local_database) {
            m_local_database->ProcessOperations();
        }

    TRY_END
}

void M2Server::ProcessTimerEvents() {
    TRY_BEGIN
        // Update server statistics
        UpdateServerStatistics();

        // Check system resources
        CheckSystemResources();

        // Process scheduled events
        ProcessScheduledEvents();

    TRY_END
}

void M2Server::ProcessMaintenanceTasks() {
    TRY_BEGIN
        static DWORD last_hourly_maintenance = 0;
        static DWORD last_daily_maintenance = 0;

        DWORD current_time = GetTickCount();

        // Hourly maintenance (every hour)
        if (current_time - last_hourly_maintenance >= 3600000) {
            PerformHourlyMaintenance();
            last_hourly_maintenance = current_time;
        }

        // Daily maintenance (every 24 hours)
        if (current_time - last_daily_maintenance >= 86400000) {
            PerformDailyMaintenance();
            last_daily_maintenance = current_time;
        }

    TRY_END
}

// Processing helper methods
void M2Server::ProcessEnvironments() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_env_mutex);

        for (auto& env : m_environments) {
            if (env) {
                env->ProcessEnvironment();
            }
        }

    TRY_END
}

void M2Server::ProcessGuilds() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_guild_mutex);

        for (auto& pair : m_guilds) {
            if (pair.second) {
                pair.second->ProcessGuild();
            }
        }

    TRY_END
}

void M2Server::ProcessCastle() {
    TRY_BEGIN
        if (m_castle) {
            m_castle->ProcessCastle();
        }

    TRY_END
}

void M2Server::ProcessScheduledEvents() {
    TRY_BEGIN
        // Process scheduled server events
        // This is placeholder for future scheduled event system

    TRY_END
}

// Statistics and monitoring
void M2Server::UpdateServerStatistics() {
    TRY_BEGIN
        // Update user statistics
        if (m_user_engine) {
            m_online_user_count = m_user_engine->GetOnlineHumCount();
            m_total_user_count = m_user_engine->GetUserCount();
        }

        // Update environment statistics
        m_environment_count = static_cast<int>(m_environments.size());

        // Update guild statistics
        m_guild_count = static_cast<int>(m_guilds.size());

        // Update performance statistics
        m_server_uptime = GetTickCount() - m_start_time;

    TRY_END
}

void M2Server::CheckSystemResources() {
    TRY_BEGIN
        // Check memory usage
        MEMORYSTATUSEX mem_status = {};
        mem_status.dwLength = sizeof(MEMORYSTATUSEX);
        if (GlobalMemoryStatusEx(&mem_status)) {
            m_memory_usage = static_cast<DWORD>(mem_status.dwMemoryLoad);

            // Log warning if memory usage is high
            if (m_memory_usage > 80) {
                g_functions::MainOutMessage("Warning: High memory usage: " + std::to_string(m_memory_usage) + "%");
            }
        }

        // Check CPU usage (simplified)
        // Real CPU usage monitoring would require more complex implementation

    TRY_END
}

// Maintenance operations
void M2Server::PerformHourlyMaintenance() {
    TRY_BEGIN
        g_functions::MainOutMessage("Performing hourly maintenance...");

        // Clean up expired sessions
        if (m_user_engine) {
            m_user_engine->CleanupExpiredSessions();
        }

        // Clean up environments
        CleanupEnvironments();

        // Update statistics
        UpdateServerStatistics();

        g_functions::MainOutMessage("Hourly maintenance completed");

    TRY_END
}

void M2Server::PerformDailyMaintenance() {
    TRY_BEGIN
        g_functions::MainOutMessage("Performing daily maintenance...");

        // Save all server data
        SaveServerData();

        // Backup important data
        BackupServerData();

        // Clean up old log files
        CleanupLogFiles();

        g_functions::MainOutMessage("Daily maintenance completed");

    TRY_END
}

// Data management
void M2Server::SaveServerData() {
    TRY_BEGIN
        g_functions::MainOutMessage("Saving server data...");

        // Save user data
        if (m_user_engine) {
            m_user_engine->SaveAllUsers();
        }

        // Save guild data
        SaveGuildData();

        // Save castle data
        SaveCastleData();

        // Save environment data
        SaveEnvironmentData();

        g_functions::MainOutMessage("Server data saved successfully");

    TRY_END
}

void M2Server::SaveGuildData() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_guild_mutex);

        for (auto& pair : m_guilds) {
            if (pair.second) {
                pair.second->SaveGuildData();
            }
        }

    TRY_END
}

void M2Server::SaveCastleData() {
    TRY_BEGIN
        if (m_castle) {
            m_castle->SaveCastleData();
        }

    TRY_END
}

void M2Server::SaveEnvironmentData() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_env_mutex);

        for (auto& env : m_environments) {
            if (env) {
                env->SaveEnvironmentData();
            }
        }

    TRY_END
}

// Cleanup and backup operations
void M2Server::CleanupEnvironments() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_env_mutex);

        // Remove inactive environments
        m_environments.erase(
            std::remove_if(m_environments.begin(), m_environments.end(),
                [](const std::shared_ptr<Environment>& env) {
                    return !env || !env->IsActive();
                }),
            m_environments.end());

    TRY_END
}

void M2Server::BackupServerData() {
    TRY_BEGIN
        g_functions::MainOutMessage("Creating server data backup...");

        // Create backup directory with timestamp
        std::string backup_dir = m_data_path + "\\Backup\\" + g_functions::GetDateTimeStr();
        CreateDirectoryA(backup_dir.c_str(), nullptr);

        // Backup guild data
        std::string guild_backup = backup_dir + "\\Guild";
        CreateDirectoryA(guild_backup.c_str(), nullptr);

        // Backup castle data
        std::string castle_backup = backup_dir + "\\Castle";
        CreateDirectoryA(castle_backup.c_str(), nullptr);

        g_functions::MainOutMessage("Server data backup completed");

    TRY_END
}

void M2Server::CleanupLogFiles() {
    TRY_BEGIN
        g_functions::MainOutMessage("Cleaning up old log files...");

        // Clean up log files older than 30 days
        // This is simplified implementation

        g_functions::MainOutMessage("Log file cleanup completed");

    TRY_END
}

// Finalization methods
void M2Server::FinalizeComponents() {
    TRY_BEGIN
        g_functions::MainOutMessage("Finalizing M2Server components...");

        // Finalize in reverse order of initialization
        FinalizeCastle();
        FinalizeGuilds();
        FinalizeEnvironments();
        FinalizeDatabase();
        FinalizeNetwork();

        // Finalize core components
        if (m_local_database) {
            m_local_database->Finalize();
            m_local_database.reset();
        }

        if (m_user_engine) {
            m_user_engine->Finalize();
            m_user_engine.reset();
        }

        g_functions::MainOutMessage("M2Server components finalized");

    TRY_END
}

void M2Server::FinalizeNetwork() {
    TRY_BEGIN
        if (m_gate_socket) {
            m_gate_socket->Finalize();
            m_gate_socket.reset();
        }

        if (m_run_socket) {
            m_run_socket->Finalize();
            m_run_socket.reset();
        }

    TRY_END
}

void M2Server::FinalizeDatabase() {
    TRY_BEGIN
        // Database finalization is handled by LocalDatabase component

    TRY_END
}

void M2Server::FinalizeEnvironments() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_env_mutex);

        for (auto& env : m_environments) {
            if (env) {
                env->Finalize();
            }
        }
        m_environments.clear();

    TRY_END
}

void M2Server::FinalizeGuilds() {
    TRY_BEGIN
        std::lock_guard<std::mutex> lock(m_guild_mutex);

        for (auto& pair : m_guilds) {
            if (pair.second) {
                pair.second->Finalize();
            }
        }
        m_guilds.clear();

    TRY_END
}

void M2Server::FinalizeCastle() {
    TRY_BEGIN
        if (m_castle) {
            m_castle->Finalize();
            m_castle.reset();
        }

    TRY_END
}

// Utility methods
void M2Server::SetServerState(ServerState state) {
    ServerState old_state = m_server_state.load();
    m_server_state.store(state);

    if (old_state != state) {
        NotifyStateChange(old_state, state);
    }
}

void M2Server::NotifyStateChange(ServerState old_state, ServerState new_state) {
    TRY_BEGIN
        std::string old_str = ServerStateToString(old_state);
        std::string new_str = ServerStateToString(new_state);

        g_functions::MainOutMessage("Server state changed: " + old_str + " -> " + new_str);

        // Notify components of state change
        if (m_user_engine) {
            m_user_engine->OnServerStateChanged(new_state);
        }

    TRY_END
}

bool M2Server::ShouldContinueRunning() const {
    return !m_shutdown_requested.load() &&
           !m_restart_requested.load() &&
           IsRunning();
}

std::string M2Server::ServerStateToString(ServerState state) const {
    switch (state) {
        case ServerState::STOPPED: return "STOPPED";
        case ServerState::INITIALIZING: return "INITIALIZING";
        case ServerState::STARTING: return "STARTING";
        case ServerState::RUNNING: return "RUNNING";
        case ServerState::STOPPING: return "STOPPING";
        case ServerState::SERVER_ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

// Error handling
void M2Server::SetLastError(const std::string& error) {
    m_last_error = error;
    g_functions::MainOutMessage("Error: " + error);
}

// GetLastError is already implemented inline in the header file

// Additional helper methods needed for compilation
// Note: Some methods are already implemented above, this section provides missing ones
